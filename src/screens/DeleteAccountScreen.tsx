"use client";

import { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { useSelector } from "react-redux";
import { API_ENDPOINTS, BRAND_ID, BUSINESS_ID } from "../config";

// Define types for Redux state
interface RootState {
  auth: {
    userData: {
      user_id: string;
      user_email: string;
      user_fullname: string;
      user_cphone: string;
      user_address: string;
      td_user_id: string;
    } | null;
  };
}

const DeleteAccountScreen = () => {
  const navigation = useNavigation();
  const { userData } = useSelector((state: RootState) => state.auth);
  const [isLoading, setIsLoading] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastType, setToastType] = useState("error");

  // Show toast message
  const showToastMessage = (message: string, type: string = "error") => {
    setToastMessage(message);
    setToastType(type);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);
  };

  // Handle account deletion request
  const handleDeleteAccount = async () => {
    setIsLoading(true);

    try {
      // Create form data for x-www-form-urlencoded format
      const formData = new FormData();
      formData.append("eatout_id", BUSINESS_ID);
      formData.append("brand_id", BRAND_ID);
      formData.append("user_id", userData?.user_id || "");
      formData.append(
        "td_user_id",
        userData?.td_user_id || userData?.user_id || ""
      );
      formData.append("user_name", userData?.user_fullname || "");
      formData.append("user_email", userData?.user_email || "");
      formData.append("user_phone", userData?.user_cphone || "");
      formData.append("user_address", userData?.user_address || "");
      formData.append("request_type", "deletion");

      const response = await fetch(API_ENDPOINTS.ACCOUNT_DELETION, {
        method: "POST",
        body: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      const data = await response.json();
      console.log("Account deletion response:", data);

      if (data.status === 1 && data.message === "success") {
        showToastMessage(
          "Your request has been successfully posted, our team will contact you within 35 days for further processing",
          "success"
        );

        // Navigate back to profile after showing success message
        setTimeout(() => {
          navigation.goBack();
        }, 3000);
      } else {
        showToastMessage(data.message || "Failed to submit deletion request");
      }
    } catch (error) {
      console.error("Error submitting deletion request:", error);
      showToastMessage("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#000000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Delete Account</Text>
        <View style={styles.headerSpacer} />
      </View>

      <View style={styles.container}>
        {/* Information Text */}
        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>Please review your information</Text>
          <Text style={styles.infoSubtitle}>
            You can visit your profile to edit this information
          </Text>
        </View>

        {/* User Information Fields */}
        <View style={styles.fieldsContainer}>
          <View style={styles.fieldGroup}>
            <Text style={styles.fieldLabel}>Name</Text>
            <View style={styles.fieldValue}>
              <Text style={styles.fieldText}>
                {userData?.user_fullname || "Not provided"}
              </Text>
            </View>
          </View>

          <View style={styles.fieldGroup}>
            <Text style={styles.fieldLabel}>Email</Text>
            <View style={styles.fieldValue}>
              <Text style={styles.fieldText}>
                {userData?.user_email || "Not provided"}
              </Text>
            </View>
          </View>

          <View style={styles.fieldGroup}>
            <Text style={styles.fieldLabel}>Phone</Text>
            <View style={styles.fieldValue}>
              <Text style={styles.fieldText}>
                {userData?.user_cphone || "Not provided"}
              </Text>
            </View>
          </View>
        </View>

        {/* Delete Button */}
        <TouchableOpacity
          style={[styles.deleteButton, isLoading && styles.disabledButton]}
          onPress={handleDeleteAccount}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.deleteButtonText}>Delete My Account</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Toast Message */}
      {showToast && (
        <View
          style={[
            styles.toast,
            toastType === "success" ? styles.successToast : styles.errorToast,
          ]}
        >
          <Text style={styles.toastText}>{toastMessage}</Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#F0F0F0",
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
    textAlign: "center",
    fontFamily: "PlusJakartaSans-Bold",
  },
  headerSpacer: {
    width: 40,
  },
  container: {
    flex: 1,
    padding: 20,
  },
  infoSection: {
    marginBottom: 32,
  },
  infoTitle: {
    fontSize: 16,
    color: "#666666",
    marginBottom: 8,
    fontFamily: "PlusJakartaSans-Regular",
  },
  infoSubtitle: {
    fontSize: 14,
    color: "#666666",
    fontFamily: "PlusJakartaSans-Regular",
  },
  fieldsContainer: {
    flex: 1,
  },
  fieldGroup: {
    marginBottom: 24,
  },
  fieldLabel: {
    fontSize: 14,
    color: "#666666",
    marginBottom: 8,
    fontFamily: "PlusJakartaSans-Regular",
  },
  fieldValue: {
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 14,
  },
  fieldText: {
    fontSize: 16,
    color: "#000000",
    fontFamily: "PlusJakartaSans-Regular",
  },
  deleteButton: {
    backgroundColor: "#000000",
    borderRadius: 100,
    paddingVertical: 16,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 20,
  },
  disabledButton: {
    backgroundColor: "#CCCCCC",
  },
  deleteButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
    fontFamily: "PlusJakartaSans-Bold",
  },
  toast: {
    position: "absolute",
    bottom: 60,
    left: 20,
    right: 20,
    padding: 12,
    borderRadius: 80,
    alignItems: "center",
  },
  successToast: {
    backgroundColor: "rgba(0, 128, 0, 1)",
  },
  errorToast: {
    backgroundColor: "rgba(255, 0, 0, 1)",
  },
  toastText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
  },
});

export default DeleteAccountScreen;
